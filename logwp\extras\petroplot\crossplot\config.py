"""logwp.extras.petroplot.crossplot.config - 交会图配置模型

本模块定义了交会图组件所需的所有Pydantic配置模型。
它结合了原型代码的详细绘图模型和组件化架构所需的数据选择能力。

Architecture
------------
层次/依赖: petroplot/crossplot配置层，被facade层使用
设计原则: 关注点分离、类型安全、用户友好
"""
from enum import Enum
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, field_validator

from logwp.extras.plotting import PlotStyle


class ScaleType(str, Enum):
    """Supported axis scale types (Plotly 6.0原生支持的类型)."""
    LINEAR = "linear"
    LOG = "log"  # Plotly原生支持，要求数据为正数
    # 注意：Plotly不支持symlog（对称对数）刻度
    # 如需symlog功能，请在数据预处理阶段进行变换


class SeriesType(str, Enum):
    """Supported series visualization types."""
    SCATTER = "scatter"
    LINE = "line"
    BOTH = "both"


class MarginalKind(str, Enum):
    """Supported marginal plot types (Plotly 6.0原生支持)."""
    HIST = "hist"
    BOX = "box"
    VIOLIN = "violin"
    KDE = "kde"  # 使用histogram with density实现
    # 注意：不支持CDF、CCDF、PROB、BOXEN等类型
    # 这些需要额外的数据处理或其他库支持


class MarginalEnabled(str, Enum):
    """Marginal plot enablement options."""
    NONE = "none"
    X = "x"
    Y = "y"
    BOTH = "both"


class TickConfig(BaseModel):
    """Configuration for axis ticks (功能性配置，样式在StyleConfig中)."""
    mode: str = "auto"  # "auto", "linear"
    interval: Optional[float] = None  # dtick - 主刻度间隔
    start: Optional[float] = None     # tick0 - 起始位置

    # 主刻度位置
    position: str = "outside"  # "inside", "outside", ""

    # 次要刻度配置
    minor_enabled: bool = False
    minor_interval: Optional[float] = None  # minor dtick
    minor_start: Optional[float] = None     # minor tick0
    minor_position: str = "outside"         # minor ticks position


class GridConfig(BaseModel):
    """Configuration for axis grid lines (功能性配置，样式在StyleConfig中)."""
    major_enabled: bool = True
    major_dash: str = "solid"  # "solid", "dot", "dash", "dashdot"

    minor_enabled: bool = False
    minor_dash: str = "dot"


class AxisTitleConfig(BaseModel):
    """Configuration for axis title."""
    text: str = "Axis Label"
    standoff: Optional[float] = None  # 标题与轴的距离
    side: str = "bottom"  # "top", "bottom", "left", "right"


class AxisRangeConfig(BaseModel):
    """Configuration for axis range."""
    mode: str = "normal"  # "normal", "tozero", "nonnegative"
    min_value: Optional[float] = None  # 手动设置最小值
    max_value: Optional[float] = None  # 手动设置最大值
    auto_margin: bool = True  # 自动边距
    constrain: Optional[str] = None  # "range", "domain"


class AxisConfig(BaseModel):
    """Configuration for a single axis."""
    title: AxisTitleConfig = Field(default_factory=AxisTitleConfig)
    scale: ScaleType = ScaleType.LINEAR
    log_base: Optional[float] = None
    inverted: bool = False
    range: AxisRangeConfig = Field(default_factory=AxisRangeConfig)

    # 刻度标签配置
    show_tick_labels: bool = True
    tick_angle: Optional[float] = None  # 刻度标签角度
    tick_prefix: str = ""  # 刻度标签前缀
    tick_suffix: str = ""  # 刻度标签后缀
    tick_format: Optional[str] = None  # d3格式字符串

    # 精细刻度和网格控制
    ticks: TickConfig = Field(default_factory=TickConfig)
    grid: GridConfig = Field(default_factory=GridConfig)


class LegendConfig(BaseModel):
    """Configuration for legend display."""
    enabled: bool = True
    position: str = "right"  # "right", "left", "top", "bottom"

    # 位置精确控制
    x: Optional[float] = None  # 0-1之间，相对位置
    y: Optional[float] = None  # 0-1之间，相对位置
    xanchor: str = "auto"  # "auto", "left", "center", "right"
    yanchor: str = "auto"  # "auto", "top", "middle", "bottom"

    # 布局控制
    orientation: str = "v"  # "v" (vertical), "h" (horizontal)
    itemsizing: str = "trace"  # "trace", "constant"
    itemwidth: Optional[float] = None  # 图例项宽度
    itemclick: str = "toggle"  # "toggle", "toggleothers", False
    itemdoubleclick: str = "toggleothers"  # "toggle", "toggleothers", False

    # 边框和背景
    borderwidth: float = 0
    bgcolor: Optional[str] = None  # 背景色，None表示透明

    # 分组
    groupclick: str = "toggleitem"  # "toggleitem", "togglegroup"
    grouptitlefont_size: Optional[float] = None


class MarkerConfig(BaseModel):
    """Configuration for scatter plot markers."""
    symbol: str = "circle"  # Plotly支持的所有符号
    size: Union[float, List[float]] = 18
    facecolor: Optional[str] = None
    linecolor: str = "white"
    edgewidth: float = 1.0

    # 高级标记配置
    opacity: Optional[float] = None
    sizemode: str = "diameter"  # "diameter", "area"
    sizeref: Optional[float] = None  # 标记大小参考值
    sizemin: Optional[float] = None  # 最小标记大小
    maxdisplayed: Optional[int] = None  # 最大显示数量


class LineConfig(BaseModel):
    """Configuration for line plots."""
    style: str = "solid"  # "solid", "dash", "dot", "dashdot", "longdash", "longdashdot"
    color: Optional[str] = None
    width: float = 2.0

    # 高级线条配置
    shape: str = "linear"  # "linear", "spline", "hv", "vh", "hvh", "vhv"
    smoothing: Optional[float] = None  # 0-1.3，仅用于spline
    simplify: bool = True  # 简化线条以提高性能
    connectgaps: bool = False  # 是否连接数据间隙


class HoverConfig(BaseModel):
    """Configuration for hover information."""
    enabled: bool = True
    mode: str = "closest"  # "closest", "x", "y", "x unified", "y unified"

    # 悬停模板
    template: Optional[str] = None  # 自定义悬停模板
    name: Optional[str] = None  # 悬停时显示的名称

    # 悬停标签配置
    bgcolor: Optional[str] = None  # 背景色
    bordercolor: Optional[str] = None  # 边框色
    align: str = "auto"  # "left", "right", "auto"

    # 悬停距离
    hoverdistance: int = 20  # 鼠标距离阈值
    spikedistance: int = 20  # 尖峰线距离阈值


class ErrorBarConfig(BaseModel):
    """Configuration for error bars."""
    visible: bool = False
    x_values: Optional[str] = None
    y_values: Optional[str] = None
    style: str = "symmetric"
    color: Optional[str] = None
    thickness: float = 1.0

    # 高级误差线配置
    type: str = "data"  # "data", "percent", "sqrt", "constant"
    symmetric: bool = True  # 是否对称
    array_minus: Optional[List[float]] = None  # 非对称误差的下限
    cap_size: float = 0  # 端点大小


class SeriesConfig(BaseModel):
    """Configuration for a data series."""
    id: str = Field(..., description="数据系列的唯一标识符，用于图例。")
    type: SeriesType = SeriesType.SCATTER
    marker: MarkerConfig = Field(default_factory=MarkerConfig)
    line: LineConfig = Field(default_factory=LineConfig)
    error_bars: ErrorBarConfig = Field(default_factory=ErrorBarConfig)
    hover: HoverConfig = Field(default_factory=HoverConfig)

    # --- Data Selectors (Logical Names) ---
    bundle_name: str = Field("default", description="此系列数据源的逻辑名称。")
    x_curve: str = Field(..., description="此系列X轴的逻辑曲线名。")
    y_curve: str = Field(..., description="此系列Y轴的逻辑曲线名。")
    z_curve: Optional[str] = Field(None, description="此系列用于颜色映射的逻辑曲线名。")
    hover_extra_curves: Optional[List[str]] = Field(None, description="在悬停提示框中为此系列额外显示的逻辑曲线名列表。")
    split_by_curve: Optional[str] = Field(None, description="（可选）用于拆分此系列数据的逻辑曲线名。")

    # 图例配置
    showlegend: bool = True
    legendgroup: Optional[str] = None  # 图例分组
    legendrank: Optional[int] = None  # 图例排序

    # 填充配置
    fill: Optional[str] = None  # "none", "tozeroy", "tozerox", "tonexty", "tonextx", "toself", "tonext"
    fillcolor: Optional[str] = None

    # 独立颜色轴配置（用于双颜色轴等高级功能）
    colorbar: Optional["ColorbarConfig"] = None  # 如果设置，该系列使用独立的颜色轴
    colorscale: Optional[str] = None  # 该系列专用的颜色映射


class ColorbarConfig(BaseModel):
    """Configuration for color mapping."""
    visible: bool = False
    cmap: str = "Viridis"  # Plotly支持的所有colorscale
    title: str = "Z Value"
    orientation: str = "vertical"  # "vertical", "horizontal"

    # 位置和尺寸控制
    x: Optional[float] = None  # 0-1之间，相对位置
    y: Optional[float] = None  # 0-1之间，相对位置
    xanchor: str = "left"  # "left", "center", "right"
    yanchor: str = "bottom"  # "top", "middle", "bottom"
    len: float = 1.0  # 长度比例
    thickness: float = 30  # 厚度（像素）

    # 刻度控制
    tickmode: str = "auto"  # "auto", "linear", "array"
    tick0: Optional[float] = None  # 起始刻度
    dtick: Optional[float] = None  # 刻度间隔
    tickvals: Optional[List[float]] = None  # 自定义刻度位置
    ticktext: Optional[List[str]] = None  # 自定义刻度标签

    # 范围控制
    cmin: Optional[float] = None  # 最小值
    cmax: Optional[float] = None  # 最大值
    cmid: Optional[float] = None  # 中间值
    clim: Optional[List[float]] = None  # 兼容旧版本

    # 边框
    outlinecolor: Optional[str] = None  # 边框颜色
    outlinewidth: float = 1  # 边框宽度

    # NaN值处理（R-12规范）
    nan_color: Optional[str] = None  # NaN值显示颜色，None时忽略NaN点
    nan_show_legend: bool = False  # 是否在图例中显示NaN数据点
    nan_legend_text: Optional[str] = None  # 自定义NaN图例文字，None时使用默认格式


class HistogramStyleConfig(BaseModel):
    """直方图边缘图配置 (Plotly go.Histogram)."""
    opacity: float = 0.7
    histnorm: Optional[str] = None  # "density", "probability", "percent", None
    nbins: Optional[int] = None  # 箱数，None表示自动
    bargap: float = 0.1  # 柱间间隙
    marker_line_width: float = 0.5
    marker_line_color: Optional[str] = None


class BoxStyleConfig(BaseModel):
    """箱线图边缘图配置 (Plotly go.Box)."""
    opacity: float = 0.7
    line_width: float = 1.0
    boxpoints: str = "outliers"  # "all", "outliers", "suspectedoutliers", False
    boxmean: bool = False  # 显示均值
    fillcolor: Optional[str] = None  # 箱体填充色
    notched: bool = False  # 是否显示缺口
    whiskerwidth: float = 0.5  # 须线宽度


class ViolinStyleConfig(BaseModel):
    """小提琴图边缘图配置 (Plotly go.Violin)."""
    opacity: float = 0.7
    line_width: float = 1.0
    points: str = "outliers"  # "all", "outliers", "suspectedoutliers", False
    box_visible: bool = False  # 显示内部箱线图
    meanline_visible: bool = False  # 显示均值线
    side: str = "both"  # "both", "positive", "negative"
    scalemode: str = "width"  # "width", "count"
    bandwidth: Optional[float] = None  # KDE带宽


class KDEStyleConfig(BaseModel):
    """KDE边缘图配置 (基于Violin实现)."""
    opacity: float = 0.7
    line_width: float = 2.0
    fill_opacity: float = 0.3
    side: str = "positive"  # "both", "positive", "negative"
    bandwidth: Optional[float] = None  # KDE带宽，None表示自动
    smoothing: float = 1.0  # 平滑度因子


class MarginalConfig(BaseModel):
    """Configuration for marginal plots."""
    enabled: MarginalEnabled = MarginalEnabled.NONE
    kind: MarginalKind = MarginalKind.HIST
    series: Union[str, List[str]] = "all"
    bins: Union[int, List[float]] = 25
    overlay: str = "overlay"
    size_x: float = 0.15  # X轴边缘图高度占比 (0.1-0.3)
    size_y: float = 0.15  # Y轴边缘图宽度占比 (0.1-0.3)
    spacing: float = 0.01  # 边缘图与主图间距

    # Plotly 6.0高级配置 - 根据类型选择对应的样式配置
    hist_style: HistogramStyleConfig = Field(default_factory=HistogramStyleConfig)
    box_style: BoxStyleConfig = Field(default_factory=BoxStyleConfig)
    violin_style: ViolinStyleConfig = Field(default_factory=ViolinStyleConfig)
    kde_style: KDEStyleConfig = Field(default_factory=KDEStyleConfig)

    # 布局精确控制
    gap_x: float = 0.02  # X边缘图与主图的间隙
    gap_y: float = 0.02  # Y边缘图与主图的间隙

    # 颜色联动控制
    color_sync: bool = True  # 是否与主图颜色同步

    # 独立样式控制（当color_sync=False时使用）
    independent_colors: Optional[List[str]] = None

    # 交互控制
    linked_selection: bool = True  # 图例选择是否影响边缘图
    hover_sync: bool = True  # 悬停信息同步

    def get_current_style(self) -> Union[HistogramStyleConfig, BoxStyleConfig, ViolinStyleConfig, KDEStyleConfig]:
        """根据当前kind返回对应的样式配置."""
        if self.kind == MarginalKind.HIST:
            return self.hist_style
        elif self.kind == MarginalKind.BOX:
            return self.box_style
        elif self.kind == MarginalKind.VIOLIN:
            return self.violin_style
        elif self.kind == MarginalKind.KDE:
            return self.kde_style
        else:
            return self.hist_style  # 默认


class FigureConfig(BaseModel):
    """Configuration for figure-level settings."""
    title: str = "Cross-Plot"
    size: tuple = (800, 600)
    watermark: Optional[Dict[str, Any]] = None

    # 高级布局配置
    autosize: bool = True  # 自动调整大小
    margin: Dict[str, int] = Field(default_factory=lambda: {
        "l": 80, "r": 80, "t": 100, "b": 80, "pad": 0
    })

    # 悬停模式
    hovermode: str = "closest"  # "closest", "x", "y", "x unified", "y unified", False

    # 拖拽和缩放
    dragmode: str = "zoom"  # "zoom", "pan", "select", "lasso", "orbit", "turntable"

    # 工具栏
    showlegend: bool = True

    # 分隔符和网格
    separators: Optional[str] = None  # 数字分隔符，如 ".,", ".,"


class ExportConfig(BaseModel):
    """Configuration for figure export settings."""
    width_inches: float = 4.2  # 导出图片宽度，单位英寸
    height_inches: float = 3.5  # 导出图片高度，单位英寸
    dpi: int = 300  # 分辨率，dots per inch

    @property
    def width_pixels(self) -> int:
        """Calculate width in pixels based on DPI."""
        return int(self.width_inches * self.dpi)

    @property
    def height_pixels(self) -> int:
        """Calculate height in pixels based on DPI."""
        return int(self.height_inches * self.dpi)


class ReferenceLineConfig(BaseModel):
    """Configuration for reference lines."""
    visible: bool = False
    slope: float = 1.0  # 斜率，默认1:1线
    intercept: float = 0.0  # 截距
    color: Optional[str] = None  # 颜色，None时使用默认
    width: Optional[float] = None  # 线宽，None时使用默认
    style: str = "dashdot"  # 线型：solid, dash, dot, dashdot
    name: str = "Reference Line"  # 图例名称
    show_legend: bool = False  # 是否在图例中显示


class CrossPlotConfig(BaseModel):
    """Main configuration object for cross-plot generation."""
    xaxis: AxisConfig = Field(default_factory=AxisConfig)
    yaxis: AxisConfig = Field(default_factory=AxisConfig)
    series: List[SeriesConfig] = Field(default_factory=list)
    colorbar: ColorbarConfig = Field(default_factory=ColorbarConfig)
    marginal: MarginalConfig = Field(default_factory=MarginalConfig)
    figure: FigureConfig = Field(default_factory=FigureConfig)
    legend: LegendConfig = Field(default_factory=LegendConfig)
    error_bars: ErrorBarConfig = Field(default_factory=ErrorBarConfig)
    export: ExportConfig = Field(default_factory=ExportConfig)
    reference_line: Union[bool, ReferenceLineConfig] = Field(default_factory=lambda: ReferenceLineConfig(visible=False))
    style: PlotStyle = Field(..., description="绘图样式配置，必须提供。")

    @field_validator('series', mode='before')
    @classmethod
    def ensure_series_list(cls, v):
        """Ensure series is always a list."""
        if isinstance(v, dict):
            return [v]
        return v


# --- Internal Models for Resolved Column Names ---

class SeriesColumnSelectors(BaseModel):
    """【内部API】包含单个系列已解析的物理列名和样式。"""
    id: str
    bundle_name: str
    type: SeriesType
    x_col: str
    y_col: str
    z_col: Optional[str] = None
    error_x_col: Optional[str] = None
    error_y_col: Optional[str] = None
    hover_extra_cols: Optional[List[str]] = None
    split_col: Optional[str] = None

    # Pass-through style overrides
    marker: MarkerConfig
    line: LineConfig
    error_bars: ErrorBarConfig
    colorbar: Optional[ColorbarConfig] = None
    colorscale: Optional[str] = None
    showlegend: bool
    legendgroup: Optional[str] = None

class CrossPlotColumnSelectors(BaseModel):
    """【内部API】包含所有已解析的系列选择器。"""
    series: List[SeriesColumnSelectors]
