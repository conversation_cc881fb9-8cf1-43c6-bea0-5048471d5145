"""logwp.extras.petroplot.crossplot.plotting - 交会图复现功能

提供从数据快照重新生成图表的功能。
"""
from __future__ import annotations

from pathlib import Path
from typing import Dict, List

import pandas as pd
import plotly.graph_objects as go

from logwp.extras.plotting import PlotStyle
from logwp.infra import get_logger

from .artifact_handler import CrossPlotArtifactHandler
from .config import CrossPlotConfig, CrossPlotColumnSelectors
from .internal.models import DataPoint
from .internal.plotter import CrossPlotter

logger = get_logger(__name__)


def _prepare_datapoints_for_replot(
    selectors: CrossPlotColumnSelectors,
    data_dict: Dict[str, pd.DataFrame],
) -> List[DataPoint]:
    """从数据源准备绘图所需的数据点列表 (复现专用)。"""
    all_datapoints = []
    for s in selectors.series:
        df = data_dict.get(s.bundle_name)
        if df is None or df.empty:
            continue

        required_cols = [c for c in [s.x_col, s.y_col, s.z_col, s.error_x_col, s.error_y_col] if c]
        if not all(c in df.columns for c in required_cols):
            logger.warning(f"系列 '{s.id}' 的数据源 '{s.bundle_name}' 缺少必需列，已跳过。")
            continue

        df_clean = df.dropna(subset=[s.x_col, s.y_col])

        for _, row in df_clean.iterrows():
            all_datapoints.append(
                DataPoint(
                    x=row[s.x_col],
                    y=row[s.y_col],
                    z=row.get(s.z_col),
                    error_x=row.get(s.error_x_col),
                    error_y=row.get(s.error_y_col),
                    series_id=s.id
                )
            )
    return all_datapoints


def replot_crossplot(
    snapshot_dir: Path,
    logic_config_path: Path,
    plot_style: PlotStyle,
    output_path: Path,
) -> None:
    """从快照文件精确复现交会图。"""
    logger.info(
        "开始从快照复现交会图",
        snapshot_dir=str(snapshot_dir),
        logic_config_path=str(logic_config_path),
        output_path=str(output_path),
    )

    handler = CrossPlotArtifactHandler()
    logic_config_data = handler.load_logic_config(logic_config_path)
    config = CrossPlotConfig(**logic_config_data['config'])
    selectors = CrossPlotColumnSelectors(**logic_config_data['selectors'])

    data_dict = {s.bundle_name: handler.load_dataframe(snapshot_dir / f"{s.bundle_name}.csv")
                 for s in selectors.series if (snapshot_dir / f"{s.bundle_name}.csv").exists()}

    config.style = plot_style
    data_points = _prepare_datapoints_for_replot(selectors, data_dict)

    plotter = CrossPlotter(config)
    fig = plotter.create_plot(data_points)

    output_path.parent.mkdir(parents=True, exist_ok=True)
    fig.write_html(output_path)
    logger.info(f"图表已从快照成功复现并保存至: {output_path}")
