"""logwp.extras.petroplot.crossplot.presets - 交会图配置预设

本模块提供了一系列便捷函数，用于快速生成针对特定场景的 `CrossPlotConfig`
对象。这旨在简化用户操作，避免手动构建复杂的配置模型。

Architecture
------------
层次/依赖: petroplot/crossplot便捷方法层，被最终用户调用
设计原则: 用户友好、封装复杂性、提供最佳实践、样式与内容分离
"""

from typing import Optional, Tuple

from logwp.extras.plotting import PlotStyle
from logwp.extras.plotting import registry
from .constants import CrossPlotProfiles
from .config import (
    AxisConfig,
    AxisRangeConfig,
    AxisTitleConfig,
    ColorbarConfig,
    CrossPlotConfig,
    ExportConfig,
    FigureConfig,
    ReferenceLineConfig,
    ScaleType,
    SeriesConfig,
    MarginalConfig,
    MarginalEnabled,
    MarginalKind,
)


def create_publication_config(
    *,
    # Data selectors
    x_curve: str,
    y_curve: str,
    z_curve: Optional[str] = None,
    series_id: str = "Data",
    bundle_name: str = "default",
    # Titles
    main_title: str,
    x_title: str,
    y_title: str,
    z_title: Optional[str] = None,
    # Axis configuration
    x_log: bool = False,
    y_log: bool = False,
    x_range: Optional[Tuple[float, float]] = None,
    y_range: Optional[Tuple[float, float]] = None,
    # Feature configuration
    show_ref_line: bool = True,
    show_colorbar: bool = True,
    show_marginals: bool = True,
    # Export configuration
    width_inches: float = 4.2,
    height_inches: float = 3.5,
    dpi: int = 300,
    # --- Style Overrides ---
    font_size_pt: int = 12,
    tick_font_size_pt: int = 10,
    marker_size: int = 8,
) -> CrossPlotConfig:
    """
    创建一个适用于论文发表的CrossPlotConfig。

    此预设配置了一个单系列的散点图，并为其注入了一套适用于出版物的、
    高质量的默认样式。

    Args:
        x_curve: 用于X轴的逻辑曲线名。
        y_curve: 用于Y轴的逻辑曲线名。
        z_curve: (可选) 用于颜色映射的逻辑曲线名。
        series_id: 数据系列的标识符，用于图例。
        bundle_name: 要使用的数据源的逻辑名称。
        main_title: 图表的主标题。
        x_title: X轴的标题。
        y_title: Y轴的标题。
        z_title: (可选) 颜色条的标题。如果为None，则默认为z_curve的名称。
        x_log: 如果为True，将X轴设置为对数刻度。
        y_log: 如果为True，将Y轴设置为对数刻度。
        x_range: (可选) 一个元组 (min, max) 用于设置X轴的范围。
        y_range: (可选) 一个元组 (min, max) 用于设置Y轴的范围。
        show_ref_line: 如果为True，显示1:1参考线。
        show_colorbar: 如果为True且提供了z_curve，则显示颜色条。
        show_marginals: 如果为True，显示边缘直方图。
        width_inches: 导出图像的宽度（英寸）。
        height_inches: 导出图像的高度（英寸）。
        dpi: 导出图像的分辨率（DPI）。
        font_size_pt: 主要字体大小（如坐标轴标签）。
        tick_font_size_pt: 刻度标签字体大小。
        marker_size: 散点标记的大小。

    Returns:
        一个完全配置好的、包含内容和样式的CrossPlotConfig对象。
    """
    # 1. Get the default style from the registry and apply user overrides
    base_style = registry.get(CrossPlotProfiles.DEFAULT.value, expected_type=PlotStyle)

    style_updates = {
        "font": {
            "size_title": font_size_pt + 2,
            "size_label": font_size_pt,
            "size_ticks": tick_font_size_pt,
        },
        "marker": {
            "default_size": marker_size,
        }
    }
    # Use model_copy for immutable update of Pydantic model
    publication_style = base_style.model_copy(update=style_updates, deep=True)

    # 2. Define the data series
    series_list = [
        SeriesConfig(id=series_id, bundle_name=bundle_name, x_curve=x_curve, y_curve=y_curve, z_curve=z_curve)
    ]

    # 3. Build the final CrossPlotConfig object
    config = CrossPlotConfig(
        figure=FigureConfig(title=main_title, size=(int(width_inches * dpi), int(height_inches * dpi))),
        xaxis=AxisConfig(title=AxisTitleConfig(text=x_title), scale=ScaleType.LOG if x_log else ScaleType.LINEAR, range=AxisRangeConfig(min_value=x_range[0] if x_range else None, max_value=x_range[1] if x_range else None)),
        yaxis=AxisConfig(title=AxisTitleConfig(text=y_title), scale=ScaleType.LOG if y_log else ScaleType.LINEAR, range=AxisRangeConfig(min_value=y_range[0] if y_range else None, max_value=y_range[1] if y_range else None)),
        series=series_list,
        colorbar=ColorbarConfig(visible=show_colorbar and z_curve is not None, title=z_title or z_curve or ""),
        marginal=MarginalConfig(enabled=MarginalEnabled.BOTH if show_marginals else MarginalEnabled.NONE, kind=MarginalKind.HIST),
        reference_line=ReferenceLineConfig(visible=show_ref_line),
        export=ExportConfig(width_inches=width_inches, height_inches=height_inches, dpi=dpi),
        style=publication_style,
    )

    return config
