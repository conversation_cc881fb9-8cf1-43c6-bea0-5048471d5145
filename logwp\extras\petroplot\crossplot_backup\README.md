# `logwp.extras.petroplot.crossplot` - 多系列、多数据源交会图组件

**版本**: 2.0 (重构后)
**状态**: 生产就绪

## 1. 摘要 (Abstract)

`crossplot` 是一个功能强大的、可被工作流编排的绘图组件，专门用于生成高度可定制的二维交会图。它基于 `petroplot` 新的模块化架构构建，是展示如何处理**多系列、多数据源**绘图场景的典范。

---

## 2. 架构与设计亮点 (Architectural Highlights)

本组件的实现严格遵循了 `petroplot` 框架确立的最佳实践，特别是在处理复杂性方面。

1.  **“总指挥”与“零件供应商”模式**:
    *   `CrossPlotter` 的 `_apply_layout` 方法完美地扮演了**“总指挥”**的角色。它负责编排整个布局过程：准备自身特有的坐标轴配置，从 `common` 包中的 `LayoutManager` 和 `LegendManager`（**“零件供应商”**）获取标准布局零件，并最终将所有部分组装成一个完整的、一致的图表布局。

2.  **通过组合管理复杂性 (SRP)**:
    *   面对交会图与边缘分布图在逻辑上的巨大差异，我们没有将所有逻辑都堆砌在 `CrossPlotter` 中，而是遵循**单一职责原则 (Single Responsibility Principle)**，创建了一个新的辅助类 `MarginalPlotHelper`。
    *   `MarginalPlotHelper` **封装了所有**与边缘图相关的复杂性，包括子图的创建、数据聚合、轨迹绘制等。`CrossPlotter` 只需在需要时实例化并调用它，而无需关心其内部实现。这使得 `CrossPlotter` 的代码更简洁，职责更聚焦于交会图本身。

3.  **健壮的多系列与多数据源处理**:
    *   `_add_data_traces` 方法中的循环逻辑，能够优雅地处理来自不同数据源（`data_dict`）的多个系列。
    *   通过在循环内部增加**前置列存在性检查**，我们从根本上消除了因配置错误（如列名拼写错误）而导致 `KeyError` 崩溃的风险，极大地提升了组件的健壮性。

4.  **正确的聚合逻辑**:
    *   `MarginalPlotHelper` 的 `draw_marginal_plots` 方法正确地实现了**先聚合、后绘制**的逻辑。它会收集所有被标记的系列的数据，然后为X轴和Y轴各绘制**一个**聚合后的边缘图，这避免了为每个系列都绘制一个重叠的直方图的严重逻辑错误。

---

## 3. 核心功能一览 (Core Features at a Glance)

*   **多系列与多数据源**: 可在同一张图上绘制多个散点图或折线图系列，每个系列可来自不同的数据源 (`WpDataFrameBundle`)。
*   **可定制的绘图类型**: 每个系列可独立配置为散点图 (`scatter`) 或折线图 (`line`)。
*   **边缘分布图**: 支持在主图的X轴和Y轴上添加聚合的边缘分布图，可选类型包括直方图 (`histogram`)、箱线图 (`box`) 和地毯图 (`rug`)。
*   **高级数据映射**:
    *   **颜色映射**: 支持按连续值（如渗透率）或分类值（如岩性）对散点进行着色。
    *   **符号映射**: 支持按分类值（如岩性）为散点分配不同的标记符号。
*   **参考线与背景**:
    *   **对角线**: 可配置在主绘图区绘制一条 `y = a*x + b` 形式的参考对角线。
    *   **背景色**: 可自定义主绘图区的背景颜色。
*   **绘图拆分/分面**: 支持根据指定列的唯一值，将图表拆分为多个子图，每个子图展示一个数据子集。
*   **灵活的空值处理**: 可选择是否将颜色列中的空值点用不同的样式独立显示，以便于数据质量诊断。
*   **丰富的悬停信息**: 自动生成包含坐标、井名、深度和所有映射列的悬停提示框，并支持添加额外的自定义信息。

---

## 4. API 使用指南与示例

### 4.1. 步骤门面 (Step Facade) - 标准工作流用法

`run_crossplot_step` 是与工作流集成的标准入口。它能够处理来自多个上游步骤的数据。

```python
# 在一个工作流脚本中
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.petroplot.crossplot import (
    run_crossplot_step,
    CrossPlotConfig,
    SeriesConfig,
    AxisConfig,
    ContinuousColorConfig,
    CategoricalColorConfig,
    DiagonalLineConfig,
    MarginalPlotConfig
)

# 假设 core_bundle 和 sw_bundle 是两个上游步骤产出的 WpDataFrameBundle

with RunContext(run_dir="./my_crossplot_run") as ctx:
    # 1. 定义多个系列，每个系列可以有不同的数据源和样式
    series1 = SeriesConfig(
        name="砂岩数据",
        bundle_name="core_analysis",      # 数据源1
        x_curve="PHI", y_curve="PERM",   # 逻辑曲线名
        color_curve="SW",               # 按饱和度着色
        color_mapping=ContinuousColorConfig(log_transform=False),
        include_in_marginals=True      # 将此系列数据包含在边缘图的统计中
    )

    series2 = SeriesConfig(
        name="饱和度模型",
        bundle_name="sw_model_results",      # 数据源2
        x_curve="PHI_MODEL", y_curve="PERM_MODEL", # 逻辑曲线名
        plot_type="line",                      # 绘制为折线图
        line_style={"color": "red", "dash": "dash"}
    )

    # 2. 定义绘图逻辑，包括启用边缘图、对角线和拆分
    config = CrossPlotConfig(
        title="孔隙度 vs 渗透率 交会图",
        series=[series1, series2],
        x_axis=AxisConfig(label="孔隙度 (%)", min=0, max=0.3),
        y_axis=AxisConfig(label="渗透率 (mD)", scale="log", min=0.1, max=10000),
        marginal_x=MarginalPlotConfig(show=True, plot_type="histogram", size=0.2),
        marginal_y=MarginalPlotConfig(show=True, plot_type="box", size=0.2),
        diagonal_line=DiagonalLineConfig(show=True, slope=1, intercept=0)
    )

    # 3. 执行步骤 (bundles字典的键必须与SeriesConfig中的bundle_name匹配)
    results = run_crossplot_step(
        config=config,
        ctx=ctx,
        prefix="final_analysis",
        bundles={
            "core_analysis": core_bundle,
            "sw_model_results": sw_bundle
        }
    )
```

### 4.2. 普通门面 (Normal Facade) - 独立脚本用法

`generate_crossplot` 接收一个包含多个 `pandas.DataFrame` 的字典，允许在工作流之外独立使用。

```python
import pandas as pd
from logwp.extras.plotting import registry
from logwp.extras.petroplot.crossplot import (
    generate_crossplot,
    CrossPlotConfig,
    CrossPlotColumnSelectors,  # 注意：这里使用物理列名选择器
    SeriesColumnSelectors,
    CrossPlotProfiles
)

# 1. 准备多个 pandas DataFrame
df1 = pd.DataFrame({"CORE_PHI": [0.1, 0.2], "CORE_PERM": [10, 100], "WELL": "W1", "MD": [100, 101]})
df2 = pd.DataFrame({"MODEL_PHI": [0.12, 0.22], "MODEL_PERM": [15, 120], "WELL": "W1", "MD": [100, 101]})

# 2. 定义数据源字典
data_dict = {"core": df1, "model": df2}

# 3. 定义多个系列选择器 (使用物理列名)
series1 = SeriesColumnSelectors(name="Core", bundle_name="core", x_col="CORE_PHI", y_col="CORE_PERM", well_col="WELL", depth_col="MD")
series2 = SeriesColumnSelectors(name="Model", bundle_name="model", x_col="MODEL_PHI", y_col="MODEL_PERM", plot_type="line", well_col="WELL", depth_col="MD")
selectors = CrossPlotColumnSelectors(series=[series1, series2])

# 4. 生成图表
figs_dict = generate_crossplot(
    config=CrossPlotConfig(title="My Standalone Crossplot", series=[]),  # series在selectors中定义
    selectors=selectors,
    data_dict=data_dict,
    plot_profile=registry.get(CrossPlotProfiles.DEFAULT.value)
)

if "main" in figs_dict:
    figs_dict["main"].show()
```

---

## 5. 配置详解 (Configuration Deep Dive)

### 5.1. `CrossPlotConfig` - 图表级配置

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `title` | `str` | 图表主标题。 |
| `x_axis`, `y_axis` | `AxisConfig` | X轴和Y轴的共享配置（标签、范围、尺度等）。 |
| `series` | `List[SeriesConfig]` | **核心配置**，定义要在图上绘制的所有数据系列。 |
| `marginal_x`, `marginal_y` | `MarginalPlotConfig` | （可选）配置X轴和Y轴的边缘分布图。 |
| `diagonal_line` | `DiagonalLineConfig` | （可选）配置参考对角线。 |
| `plot_bgcolor` | `str` | （可选）主绘图区的背景颜色。 |
| `split_subtitle_template` | `str` | 当按列拆分图表时，用于生成子图副标题的模板。 |
| `max_split_plots` | `int` | 允许生成的最大子图数量，防止意外生成过多图表。 |

### 5.2. `SeriesConfig` - 系列级配置

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `name` | `str` | 系列名称，用于图例。 |
| `bundle_name` | `str` | 此系列的数据源逻辑名，对应传入 `run_crossplot_step` 的 `bundles` 字典的键。 |
| `plot_type` | `"scatter"` 或 `"line"` | 绘图类型。 |
| `x_curve`, `y_curve` | `str` | X轴和Y轴的逻辑曲线名。 |
| `color_curve` | `str` | （可选）用于颜色映射的逻辑曲线名。 |
| `symbol_curve` | `str` | （可选）用于符号映射的逻辑曲线名。 |
| `split_by_curve` | `str` | （可选）用于拆分图表的逻辑曲线名。 |
| `color_mapping` | `ContinuousColorConfig` 或 `CategoricalColorConfig` | （可选）此系列的颜色映射配置。 |
| `symbol_mapping` | `SymbolConfig` | （可选）此系列的符号映射配置。 |
| `distinguish_null_color` | `bool` | 是否将颜色列中的空值点用不同样式独立显示。 |
| `include_in_marginals` | `bool` | 是否将此系列的数据包含在边缘图的统计中。 |

---

## 6. 目录结构

`crossplot` 组件的目录结构清晰地反映了其模块化的设计。

```text
logwp/extras/petroplot/crossplot/
├── __init__.py
├── README.md                 # 本文档
├── facade.py                 # 实现三层API函数
├── config.py                 # 定义 CrossPlot 特有配置
└── internal/
    ├── plotter.py            # 核心绘图引擎 (CrossPlotter)
    └── marginal_plot_helper.py # 【新】边缘图绘制辅助类
```
