"""logwp.extras.petroplot.crossplot.constants - 交会图常量

本模块定义了交会图组件所需的所有常量，包括产物逻辑名称和
将在全局注册表中注册的PlotProfile模板名称。

Architecture
------------
层次/依赖: petroplot/crossplot配置层，被各模块引用
设计原则: 避免魔法字符串、保证全局唯一性
"""
from enum import Enum


class CrossPlotArtifacts(str, Enum):
    """定义交会图步骤的所有产物逻辑名称。"""
    # 使用前缀模式，确保在Workflow中多次调用时名称唯一。
    PLOT_PREFIX = "petroplot_crossplot.plots.crossplot"
    DATA_SNAPSHOT_DIR = "petroplot_crossplot.data_snapshots.data_sources"
    LOGIC_CONFIG = "petroplot_crossplot.configs.logic_config"


class CrossPlotProfiles(str, Enum):
    """定义将在全局注册表中注册的PlotProfile模板名称。"""
    BASE = "petroplot.crossplot.base"
    DEFAULT = "petroplot.crossplot.default"
