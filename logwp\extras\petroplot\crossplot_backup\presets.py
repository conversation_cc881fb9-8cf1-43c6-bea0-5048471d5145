"""logwp.extras.petroplot.crossplot.presets - 交会图便捷配置预设

本模块提供了一系列便捷函数，用于快速生成针对特定场景的 `CrossPlotConfig`
和 `PlotProfile` 对象。这旨在简化用户操作，避免直接处理复杂的配置模型。

Architecture
------------
层次/依赖: petroplot/crossplot便捷方法层，被最终用户调用
设计原则: 用户友好、封装复杂性、提供最佳实践
"""

from typing import Tuple, Optional, Literal

from logwp.extras.petroplot.common.config import DataMarkerStyle
from logwp.extras.plotting import PlotProfile, registry
from logwp.extras.petroplot.common import (
    AxisConfig,
    ContinuousColorConfig,
    DiagonalLineConfig,
    MarginalPlotConfig,
    NullMarkerStyle,
    LegendConfig,
    ColorBarConfig,
    LineStyle,
    TickConfig,
    LineConfig,
)
from .config import CrossPlotConfig, SeriesConfig
from .constants import CrossPlotProfiles


def create_publication_ready_perm_config(
    # --- Data parameters ---
    bundle_name: str,
    x_curve: str,
    y_curve: str,
    color_curve: str,
    # --- Axis parameters ---
    x_title: str,
    y_title: str,
    x_range: Optional[Tuple[float, float]] = None,
    y_range: Optional[Tuple[float, float]] = None,
    x_log: bool = False,
    y_log: bool = False,
    x_show_grid: bool = True,
    y_show_grid: bool = True,
    # --- Feature parameters ---
    show_marginal_x: bool = False,
    show_marginal_y: bool = False,
    show_diagonal_line: bool = True,
    # --- Style parameters ---
    title: Optional[str] = None,
    colorscale: str = "Plasma",
    cmin: float = -2.0,
    cmax: float = 3.0,
    distinguish_null_color: bool = True,
    show_legend: bool = True,
    width_inches: float = 4.2,
    height_inches: float = 3.5,
    dpi: int = 300,
    font_size_pt: int = 10,
    tick_font_size_pt: int = 8,
    marker_size: int = 8,
    legend_position: Literal['right', 'bottom'] = 'right',
) -> Tuple[CrossPlotConfig, PlotProfile]:
    """
    创建一个适用于论文发表的、以渗透率为颜色轴的便捷配置。

    此预设针对的场景是：
    - 绘制单个散点图系列。
    - 颜色轴为对数渗透率 (log(K)) 或其他连续值。
    - 标签和图例针对出版物进行了优化。
    - 输出图像尺寸符合常见期刊单栏宽度要求。

    Args:
        bundle_name: 数据源的逻辑名称。
        x_curve, y_curve, color_curve: X轴、Y轴、颜色轴的逻辑曲线名。
        x_title, y_title, color_title: X轴、Y轴、颜色轴的标题文本。
        x_range, y_range: 坐标轴的范围，如 (min, max)。
        x_log, y_log: 坐标轴是否使用对数刻度。
        x_show_grid, y_show_grid: 是否显示主次网格线。
        show_marginal_x, show_marginal_y: 是否显示边缘直方图。
        show_diagonal_line: 是否显示1:1对角线。
        title: 图表主标题。
        colorscale: Plotly颜色映射方案。
        cmin, cmax: 颜色条范围的最小值和最大值。
        distinguish_null_color: 是否独立显示颜色列中的空值点。
        show_legend: 是否显示颜色条图例。
        width_inches, height_inches: 输出图像的物理宽度和高度（英寸）。
        dpi: 输出图像的分辨率 (dots per inch)。
        font_size_pt, tick_font_size_pt: 字体大小（点）。
        marker_size: 数据点的大小。
        legend_position: 颜色条图例的位置。

    Returns:
        一个元组，包含配置好的 (CrossPlotConfig, PlotProfile) 对象。
    """
    # --- 1. 定义单个数据系列 ---
    main_series = SeriesConfig(
        name="Data",  # 单系列名称不重要，因为主图例不显示
        bundle_name=bundle_name,
        plot_type="scatter",
        x_curve=x_curve,
        y_curve=y_curve,
        color_curve=color_curve,
        color_mapping=ContinuousColorConfig(
            colorscale=colorscale,
            cmin=cmin,
            cmax=cmax,
            log_transform=True, # 渗透率通常使用对数
        ),
        distinguish_null_color=distinguish_null_color,
        marker_style=DataMarkerStyle(label="w/ K"),
        null_marker_style=NullMarkerStyle(
            symbol="circle",
            color="darkgrey",
            label="w/o K"
        ),
        include_in_marginals=True,
    )

    # --- 2. 构建主绘图配置对象 ---
    plot_config = CrossPlotConfig(
        title=title or "",
        show_title=title is not None,
        series=[main_series],
        x_axis=AxisConfig(
            label=x_title,
            min=x_range[0] if x_range else None,
            max=x_range[1] if x_range else None,
            scale="log" if x_log else "linear",
            major_ticks=TickConfig(show=x_show_grid, grid_line=LineConfig(show=True, color="darkgrey", width=0.4, dash="solid")),
            minor_ticks=TickConfig(show=x_show_grid, grid_line=LineConfig(show=True, color="grey", width=0.2, dash="solid")),
        ),
        y_axis=AxisConfig(
            label=y_title,
            min=y_range[0] if y_range else None,
            max=y_range[1] if y_range else None,
            scale="log" if y_log else "linear",
            major_ticks=TickConfig(show=y_show_grid, grid_line=LineConfig(show=True, color="darkgrey", width=0.4, dash="solid")),
            minor_ticks=TickConfig(show=y_show_grid, grid_line=LineConfig(show=True, color="grey", width=0.2, dash="solid")),
        ),
        diagonal_line=DiagonalLineConfig(
            show=show_diagonal_line,
            line_style=LineStyle(color="red", dash="dash")
        ),
        marginal_x=MarginalPlotConfig(show=show_marginal_x, plot_type="histogram"),
        marginal_y=MarginalPlotConfig(show=show_marginal_y, plot_type="histogram"),
        # 因为只有一个系列，不显示主图例，只显示颜色条图例
        legend=LegendConfig(show=False),
        color_bar=ColorBarConfig(show=show_legend, title="log(K)"),
        legend_position=legend_position,
    )

    # --- 3. 基于默认模板修改 PlotProfile ---
    base_profile = registry.get(CrossPlotProfiles.DEFAULT.value)
    plot_profile = base_profile.with_updates(
        save_config={
            "width": width_inches,
            "height": height_inches,
            "dpi": dpi
        },
        rc_params={"font.size": font_size_pt},
        title_props={"fontsize": font_size_pt + 2},
        artist_props={
            "axis": {
                "tickfont": {"size": tick_font_size_pt},
                "titlefont": {"size": font_size_pt}
            },
            "scatter": {"size": marker_size},
            "colorbar": {
                "tickfont": {"size": tick_font_size_pt},
                "titlefont": {"size": tick_font_size_pt}
            }
        },
    )

    return plot_config, plot_profile
